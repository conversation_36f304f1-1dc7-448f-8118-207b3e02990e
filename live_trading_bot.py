#!/usr/bin/env python3
"""
Enhanced Live Trading Bot with Signal Tracking and Evaluation
Fetches live market data, provides trading signals, tracks results, and maintains statistics
"""

import time
import threading
from datetime import datetime, timedelta
import pandas as pd
import json
import os
from strategy_engine import StrategyEngine
from utils import (
    fetch_live_candles, get_current_time_info, print_colored,
    print_header, print_table_row, format_signal_output, validate_pair,
    print_signal_table_header, print_signal_row, format_price, format_percentage
)
from config import CURRENCY_PAIRS, TRADING_CONFIG, DISPLAY_CONFIG

class EnhancedLiveTradingBot:
    def __init__(self, selected_pairs=None):
        """Initialize the enhanced live trading bot"""
        self.strategy_engine = StrategyEngine()
        self.running = False
        self.pairs = selected_pairs if selected_pairs else CURRENCY_PAIRS.copy()

        # Signal tracking and statistics
        self.pending_signals = []  # Signals waiting for evaluation
        self.signal_history = []   # All processed signals
        self.statistics = {
            'total_signals': 0,
            'wins': 0,
            'losses': 0,
            'refunds': 0
        }

        # Load existing data if available
        self.load_signal_data()

    def load_signal_data(self):
        """Load existing signal data and statistics"""
        try:
            if os.path.exists('signal_data.json'):
                with open('signal_data.json', 'r') as f:
                    data = json.load(f)
                    self.pending_signals = data.get('pending_signals', [])
                    self.signal_history = data.get('signal_history', [])
                    self.statistics = data.get('statistics', {
                        'total_signals': 0,
                        'wins': 0,
                        'losses': 0,
                        'refunds': 0
                    })
        except Exception as e:
            print_colored(f"⚠️  Could not load signal data: {str(e)}", "WARNING")

    def save_signal_data(self):
        """Save signal data and statistics"""
        try:
            data = {
                'pending_signals': self.pending_signals,
                'signal_history': self.signal_history[-100:],  # Keep last 100 for performance
                'statistics': self.statistics
            }
            with open('signal_data.json', 'w') as f:
                json.dump(data, f, indent=2)
        except Exception as e:
            print_colored(f"⚠️  Could not save signal data: {str(e)}", "WARNING")

    def start(self):
        """Start the enhanced live trading bot"""
        print_header("🚀 ENHANCED LIVE TRADING BOT STARTED")
        print_colored(f"📊 Monitoring {len(self.pairs)} currency pairs", "INFO")
        print_colored(f"⏰ Fetching data every {TRADING_CONFIG['FETCH_INTERVAL']} seconds", "INFO")
        print_colored(f"🎯 Minimum confidence: {TRADING_CONFIG['MIN_CONFIDENCE']*100}%", "INFO")
        print_colored(f"📈 Signal tracking and evaluation enabled", "SUCCESS")
        print()

        self.running = True

        # Start the main trading loop
        self.trading_loop()

    def stop(self):
        """Stop the trading bot"""
        self.running = False
        self.save_signal_data()
        print_colored("\n🛑 Enhanced trading bot stopped", "WARNING")
    
    def trading_loop(self):
        """Enhanced main trading loop with signal evaluation"""
        while self.running:
            try:
                # Get current time info
                time_info = get_current_time_info()
                current_minute = time_info['current_time'].minute

                # Wait until 2 seconds before next minute
                if time_info['seconds_to_next_minute'] > 2:
                    sleep_time = time_info['seconds_to_next_minute'] - 2
                    print_colored(f"⏳ Waiting {sleep_time} seconds until next scan...", "INFO")
                    time.sleep(sleep_time)

                # Perform market scan and signal evaluation
                self.enhanced_market_scan()

                # Wait until the next minute starts (ensuring we don't skip minutes)
                while True:
                    time_info = get_current_time_info()
                    new_minute = time_info['current_time'].minute

                    # If minute has changed, break and start next scan
                    if new_minute != current_minute:
                        break

                    # Sleep for 1 second and check again
                    time.sleep(1)

            except KeyboardInterrupt:
                print_colored("\n⚠️  Interrupted by user", "WARNING")
                break
            except Exception as e:
                print_colored(f"❌ Error in trading loop: {str(e)}", "ERROR")
                time.sleep(5)  # Wait before retrying

    def enhanced_market_scan(self):
        """Enhanced market scan with signal tracking and evaluation"""
        current_time = datetime.now()

        # First, evaluate any pending signals from previous cycles
        self.evaluate_pending_signals()

        # Display the enhanced interface
        self.display_enhanced_interface(current_time)

    def display_enhanced_interface(self, current_time):
        """Display the enhanced 4-block interface"""
        print_header(f"📊 ENHANCED MARKET SCAN - {current_time.strftime('%Y-%m-%d %H:%M:%S')}")

        # Block 1: Current Signal Detection
        self.display_current_signals_block()

        # Block 2: Signals Added for Evaluation
        self.display_signals_to_evaluate_block()

        # Block 3: Signal Evaluation Results
        self.display_evaluation_results_block()

        # Block 4: Total Statistics
        self.display_statistics_block()

    def display_current_signals_block(self):
        """Block 1: Display current signal detection results"""
        print_colored("\n" + "="*80, "HEADER")
        print_colored("📈 BLOCK 1: CURRENT SIGNAL DETECTION", "HEADER", bold=True)
        print_colored("="*80, "HEADER")

        current_signals = []
        signals_found = 0

        for pair in self.pairs:
            try:
                # Fetch live data
                df = fetch_live_candles(pair, TRADING_CONFIG['LOOKBACK_CANDLES'])

                if df is not None and len(df) > 50:
                    # Evaluate strategies
                    signal_data = self.strategy_engine.evaluate_all_strategies(df)

                    if signal_data['signal'] != 'HOLD':
                        signals_found += 1
                        signal_info = {
                            'pair': pair,
                            'direction': signal_data['signal'],
                            'confidence': signal_data['confidence'],
                            'strategy': signal_data['strategy'],
                            'price': signal_data['price'],
                            'timestamp': datetime.now()
                        }
                        current_signals.append(signal_info)

                        # Display signal
                        print_colored(f"🎯 {pair} | {signal_data['signal']} | {format_percentage(signal_data['confidence']*100)} | {signal_data['strategy']}",
                                    "BUY" if signal_data['signal'] == 'BUY' else "SELL", bold=True)

            except Exception as e:
                print_colored(f"❌ Error scanning {pair}: {str(e)}", "ERROR")

        if signals_found == 0:
            print_colored("ℹ️  No signals found in all selected pairs", "INFO")

        # Store current signals for next cycle evaluation
        self.store_signals_for_evaluation(current_signals)

    def store_signals_for_evaluation(self, current_signals):
        """Store current signals for evaluation in the next cycle"""
        for signal in current_signals:
            # Get the open price of the next candle (current candle close price as approximation)
            signal['next_candle_open'] = signal['price']
            signal['evaluation_time'] = signal['timestamp'] + timedelta(minutes=1)
            self.pending_signals.append(signal)

    def display_signals_to_evaluate_block(self):
        """Block 2: Display signals that will be evaluated"""
        print_colored("\n" + "="*80, "HEADER")
        print_colored("📋 BLOCK 2: SIGNALS ADDED FOR EVALUATION", "HEADER", bold=True)
        print_colored("="*80, "HEADER")

        if self.pending_signals:
            for signal in self.pending_signals:
                eval_time = signal['evaluation_time'].strftime('%H:%M:%S')
                print_colored(f"⏰ {signal['pair']} | {signal['direction']} | Eval at {eval_time}", "WARNING")
        else:
            print_colored("ℹ️  No signals for evaluation", "INFO")

    def display_evaluation_results_block(self):
        """Block 3: Display evaluation results from the last cycle"""
        print_colored("\n" + "="*80, "HEADER")
        print_colored("📊 BLOCK 3: SIGNAL EVALUATION RESULTS", "HEADER", bold=True)
        print_colored("="*80, "HEADER")

        # Show recent evaluation results
        recent_results = [r for r in self.signal_history if
                         datetime.fromisoformat(r['evaluated_at']) > datetime.now() - timedelta(minutes=2)]

        if recent_results:
            for result in recent_results[-5:]:  # Show last 5 results
                result_icon = "✅" if result['result'] == 'WIN' else "❌" if result['result'] == 'LOSS' else "🔄"
                eval_time = datetime.fromisoformat(result['evaluated_at']).strftime('%H:%M:%S')
                print_colored(f"{result_icon} Result: {result['result']} | {result['pair']} | {result['direction']} | {eval_time}",
                            "SUCCESS" if result['result'] == 'WIN' else "ERROR" if result['result'] == 'LOSS' else "WARNING")
        else:
            print_colored("ℹ️  No signal results to display", "INFO")

    def display_statistics_block(self):
        """Block 4: Display total statistics"""
        print_colored("\n" + "="*80, "HEADER")
        print_colored("📈 BLOCK 4: TOTAL STATISTICS", "HEADER", bold=True)
        print_colored("="*80, "HEADER")

        total = self.statistics['total_signals']
        wins = self.statistics['wins']
        losses = self.statistics['losses']
        refunds = self.statistics['refunds']

        win_rate = (wins / total * 100) if total > 0 else 0

        print_colored(f"📊 Stats → Total: {total:02d} | Wins: {wins:02d} | Losses: {losses:02d} | Refunds: {refunds:02d}", "INFO", bold=True)
        if total > 0:
            print_colored(f"🎯 Win Rate: {win_rate:.1f}%", "SUCCESS" if win_rate >= 50 else "WARNING")
        print()

    def evaluate_pending_signals(self):
        """Evaluate pending signals that are ready for evaluation"""
        current_time = datetime.now()
        signals_to_remove = []

        for i, signal in enumerate(self.pending_signals):
            # Check if it's time to evaluate (after 1 minute + 2-3 seconds buffer)
            if current_time >= signal['evaluation_time'] + timedelta(seconds=3):
                try:
                    result = self.evaluate_signal(signal)
                    if result:
                        # Update statistics
                        self.statistics['total_signals'] += 1
                        if result['result'] == 'WIN':
                            self.statistics['wins'] += 1
                        elif result['result'] == 'LOSS':
                            self.statistics['losses'] += 1
                        elif result['result'] == 'REFUND':
                            self.statistics['refunds'] += 1

                        # Add to history
                        self.signal_history.append(result)

                        # Mark for removal
                        signals_to_remove.append(i)

                except Exception as e:
                    print_colored(f"❌ Error evaluating signal for {signal['pair']}: {str(e)}", "ERROR")
                    signals_to_remove.append(i)

        # Remove evaluated signals
        for i in reversed(signals_to_remove):
            self.pending_signals.pop(i)

        # Save data after evaluation
        if signals_to_remove:
            self.save_signal_data()

    def evaluate_signal(self, signal):
        """Evaluate a single signal by fetching the candle data"""
        try:
            # Fetch recent candle data
            df = fetch_live_candles(signal['pair'], 5)

            if df is not None and len(df) >= 2:
                # Get the candle that started at the evaluation time
                evaluation_candle = df.iloc[-2]  # Previous candle (completed)

                open_price = evaluation_candle['open']
                close_price = evaluation_candle['close']

                # Determine result
                if close_price == open_price:
                    result = 'REFUND'
                elif signal['direction'] == 'UP' and close_price > open_price:
                    result = 'WIN'
                elif signal['direction'] == 'DOWN' and close_price < open_price:
                    result = 'WIN'
                else:
                    result = 'LOSS'

                return {
                    'pair': signal['pair'],
                    'direction': signal['direction'],
                    'signal_time': signal['timestamp'].isoformat(),
                    'evaluated_at': datetime.now().isoformat(),
                    'open_price': open_price,
                    'close_price': close_price,
                    'result': result,
                    'strategy': signal['strategy'],
                    'confidence': signal['confidence']
                }

        except Exception as e:
            print_colored(f"❌ Error in signal evaluation: {str(e)}", "ERROR")

        return None

    def get_signal(self, pair):
        """Placeholder function for get_signal as requested"""
        try:
            df = fetch_live_candles(pair, TRADING_CONFIG['LOOKBACK_CANDLES'])
            if df is not None and len(df) > 50:
                signal_data = self.strategy_engine.evaluate_all_strategies(df)
                if signal_data['signal'] != 'HOLD':
                    return {
                        "dir": signal_data['signal'],
                        "confidence": int(signal_data['confidence'] * 100)
                    }
            return None
        except:
            return None

    def fetch_candle_open_close(self, pair, timestamp):
        """Placeholder function for fetch_candle_open_close as requested"""
        try:
            df = fetch_live_candles(pair, 5)
            if df is not None and len(df) >= 2:
                candle = df.iloc[-2]  # Get previous completed candle
                return (candle['open'], candle['close'])
            return (None, None)
        except:
            return (None, None)

def main(selected_pairs=None):
    """Main function"""
    try:
        # Create and start the enhanced trading bot
        bot = EnhancedLiveTradingBot(selected_pairs)

        print_colored("🤖 Enhanced Live Trading Bot Initializing...", "INFO", bold=True)
        print_colored("📈 Signal tracking and evaluation enabled", "SUCCESS")
        print_colored("Press Ctrl+C to stop the bot", "WARNING")
        print()

        # Start the bot
        bot.start()

    except KeyboardInterrupt:
        print_colored("\n👋 Goodbye! Enhanced trading bot stopped.", "INFO")
    except Exception as e:
        print_colored(f"❌ Fatal error: {str(e)}", "ERROR")

if __name__ == "__main__":
    main()
