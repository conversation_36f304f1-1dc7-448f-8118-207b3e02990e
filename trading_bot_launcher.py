#!/usr/bin/env python3
"""
Trading Bot Launcher
Main entry point for the trading bot system
"""

import sys
import os
from utils import print_colored, print_header, select_currency_pairs

def show_menu():
    """Display the main menu"""
    print_header("🚀 ENHANCED TRADING BOT SYSTEM")
    print_colored("Choose an option:", "INFO", bold=True)
    print()
    print_colored("1. 📈 Enhanced Rule-Based Trading (Live)", "BUY", bold=True)
    print_colored("   • Signal tracking and evaluation", "INFO")
    print_colored("   • Real-time statistics", "INFO")
    print_colored("   • Win/Loss tracking", "INFO")
    print()
    print_colored("2. 🔗 Test API Connection", "INFO", bold=True)
    print_colored("3. 📊 View Current Directory", "INFO", bold=True)
    print_colored("4. ❌ Exit", "ERROR", bold=True)
    print()

def test_api_connection():
    """Test the Oanda API connection"""
    print_header("🔗 TESTING API CONNECTION")
    try:
        import test_oanda_connection
        # The test will run automatically when imported
    except Exception as e:
        print_colored(f"❌ Error testing API: {str(e)}", "ERROR")

def start_enhanced_rule_based_trading():
    """Start the enhanced rule-based live trading bot"""
    print_header("🚀 ENHANCED RULE-BASED LIVE TRADING")
    print_colored("🔧 Using: Pure rule-based strategies (S1, S2, S3, S4)", "INFO")
    print_colored("📊 Signal Generation: Hard-coded technical analysis", "INFO")
    print_colored("📈 Signal Tracking: Real-time evaluation and statistics", "SUCCESS")
    print_colored("🎯 Features: WIN/LOSS tracking, confidence scoring", "SUCCESS")
    print_colored("⚡ Performance: Fast, transparent, and comprehensive", "SUCCESS")
    print_colored("⚠️  This will start live market monitoring with signal tracking", "WARNING", bold=True)
    print_colored("Press Ctrl+C to stop the bot at any time", "INFO")
    print()

    # Select currency pairs
    try:
        selected_pairs = select_currency_pairs()
        print()

        print_colored(f"📊 Selected {len(selected_pairs)} currency pairs for analysis", "INFO")
        print_colored("📈 Enhanced features: Signal tracking, evaluation, statistics", "SUCCESS")
        print_colored("⚠️  Ready to start enhanced live market monitoring", "WARNING", bold=True)
        print()

        confirm = input("Start enhanced rule-based live trading? (y/n): ").strip().lower()
        if confirm == 'y':
            try:
                from live_trading_bot import main
                main(selected_pairs)
            except Exception as e:
                print_colored(f"❌ Error starting enhanced trading: {str(e)}", "ERROR")
        else:
            print_colored("❌ Enhanced trading cancelled", "WARNING")
    except Exception as e:
        print_colored(f"❌ Error in currency pair selection: {str(e)}", "ERROR")



def view_directory():
    """View current directory contents"""
    print_header("📊 CURRENT DIRECTORY CONTENTS")
    
    try:
        files = os.listdir('.')
        
        # Separate files by type
        python_files = [f for f in files if f.endswith('.py')]
        csv_files = [f for f in files if f.endswith('.csv')]
        pkl_files = [f for f in files if f.endswith('.pkl')]
        directories = [f for f in files if os.path.isdir(f)]
        other_files = [f for f in files if f not in python_files + csv_files + pkl_files + directories]
        
        if python_files:
            print_colored("🐍 Python Files:", "INFO", bold=True)
            for f in sorted(python_files):
                print_colored(f"   {f}", "INFO")
            print()
        
        if csv_files:
            print_colored("📊 CSV Data Files:", "SUCCESS", bold=True)
            for f in sorted(csv_files):
                print_colored(f"   {f}", "SUCCESS")
            print()
        
        if pkl_files:
            print_colored("🤖 Model Files:", "BUY", bold=True)
            for f in sorted(pkl_files):
                print_colored(f"   {f}", "BUY")
            print()
        
        if directories:
            print_colored("📁 Directories:", "HEADER", bold=True)
            for d in sorted(directories):
                print_colored(f"   {d}/", "HEADER")
            print()
        
        if other_files:
            print_colored("📄 Other Files:", "WARNING", bold=True)
            for f in sorted(other_files):
                print_colored(f"   {f}", "WARNING")
            print()
        
        print_colored(f"Total files: {len(files)}", "INFO", bold=True)
        
    except Exception as e:
        print_colored(f"❌ Error viewing directory: {str(e)}", "ERROR")

def main():
    """Main launcher function"""
    while True:
        try:
            show_menu()
            choice = input("Enter your choice (1-4): ").strip()

            if choice == '1':
                start_enhanced_rule_based_trading()
            elif choice == '2':
                test_api_connection()
            elif choice == '3':
                view_directory()
            elif choice == '4':
                print_colored("👋 Goodbye! ✨ Hope Your Trading Was Profitable", "INFO", bold=True)
                break
            else:
                print_colored("❌ Invalid choice. Please enter 1-4.", "ERROR")

            # Wait for user to continue
            if choice in ['1', '2', '3']:
                print()
                input("Press Enter to continue...")
                print()

        except KeyboardInterrupt:
            print_colored("\n👋 Goodbye! ✨ Hope Your Trading Was Profitable", "INFO", bold=True)
            break
        except Exception as e:
            print_colored(f"❌ Unexpected error: {str(e)}", "ERROR")

if __name__ == "__main__":
    main()
